#!/bin/sh
# GPT-Load 系统状态检查脚本
# 快速检查系统运行状态

# 检查 Docker Compose 命令
if docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose 未找到"
    exit 1
fi

echo "=== GPT-Load 系统状态检查 ==="
echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo

# 1. 检查容器状态
echo "📦 容器状态:"
$DOCKER_COMPOSE_CMD ps

echo

# 2. 检查健康状态
echo "🏥 健康检查:"
HEALTHY_COUNT=$($DOCKER_COMPOSE_CMD ps | grep -c "healthy" || echo "0")
TOTAL_COUNT=$($DOCKER_COMPOSE_CMD ps | grep -c "Up" || echo "0")

if [ "$HEALTHY_COUNT" -gt 0 ]; then
    echo "✅ $HEALTHY_COUNT/$TOTAL_COUNT 服务健康"
else
    echo "⚠️  无健康检查信息或服务异常"
fi

# 3. 检查应用响应
echo
echo "🌐 应用响应:"
if curl -s --max-time 5 http://localhost:3001/health >/dev/null 2>&1; then
    RESPONSE=$(curl -s --max-time 5 http://localhost:3001/health)
    echo "✅ 应用响应正常"
    echo "   响应: $RESPONSE"
else
    echo "❌ 应用无响应或超时"
fi

# 4. 检查端口占用
echo
echo "🔌 端口状态:"
for port in 3001 5432 6379 1081; do
    if netstat -tln 2>/dev/null | grep -q ":$port "; then
        echo "✅ 端口 $port: 已监听"
    else
        echo "❌ 端口 $port: 未监听"
    fi
done

# 5. 检查磁盘空间
echo
echo "💾 磁盘空间:"
AVAILABLE_MB=$(df . | tail -1 | awk '{print int($4/1024)}')
if [ "$AVAILABLE_MB" -gt 1024 ]; then
    echo "✅ 可用空间: ${AVAILABLE_MB}MB"
else
    echo "⚠️  可用空间不足: ${AVAILABLE_MB}MB"
fi

# 6. 检查最近的错误日志
echo
echo "📋 最近错误 (最近10条):"
if $DOCKER_COMPOSE_CMD logs --tail=50 2>/dev/null | grep -i error | tail -10 | grep -q .; then
    $DOCKER_COMPOSE_CMD logs --tail=50 2>/dev/null | grep -i error | tail -10
else
    echo "✅ 无最近错误"
fi

# 7. 数据库版本检查
echo
echo "🗄️  数据库版本:"
if $DOCKER_COMPOSE_CMD exec -T postgres psql -U postgres -d gpt-load -t -c "SELECT version FROM db_version ORDER BY applied_at DESC LIMIT 1;" 2>/dev/null | grep -q .; then
    DB_VERSION=$($DOCKER_COMPOSE_CMD exec -T postgres psql -U postgres -d gpt-load -t -c "SELECT version FROM db_version ORDER BY applied_at DESC LIMIT 1;" 2>/dev/null | tr -d ' \n')
    echo "✅ 数据库版本: $DB_VERSION"
else
    echo "⚠️  无法获取数据库版本"
fi

echo
echo "=== 检查完成 ==="

# 总结状态
if [ "$HEALTHY_COUNT" -gt 0 ] && curl -s --max-time 5 http://localhost:3001/health >/dev/null 2>&1; then
    echo "🎉 系统运行正常"
    exit 0
else
    echo "⚠️  系统可能存在问题，请检查上述信息"
    exit 1
fi
