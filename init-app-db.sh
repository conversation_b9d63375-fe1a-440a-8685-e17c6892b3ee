#!/bin/sh
# 等待PostgreSQL就绪
echo "Waiting for PostgreSQL to be ready..."
until nc -z postgres 5432; do
  echo "PostgreSQL not ready yet, waiting..."
  sleep 2
done
echo "PostgreSQL is ready!"

# 等待Redis就绪
echo "Waiting for <PERSON>is to be ready..."
until nc -z redis 6379; do
  echo "Redis not ready yet, waiting..."
  sleep 2
done
echo "Redis is ready!"

# 给PostgreSQL一些额外时间来完成初始化脚本
echo "Waiting for PostgreSQL initialization to complete..."
sleep 5

echo "All dependencies are ready. Starting GPT-Load application..."

# 启动应用
exec /app/gpt-load