#!/bin/sh
# GPT-Load 智能数据库初始化和应用启动脚本
# 版本: 2.0.0
# 功能: 环境检测、数据库初始化、错误处理、自动恢复

set -e  # 遇到错误立即退出

# 配置变量
POSTGRES_HOST="${POSTGRES_HOST:-postgres}"
POSTGRES_PORT="${POSTGRES_PORT:-5432}"
POSTGRES_USER="${POSTGRES_USER:-postgres}"
POSTGRES_DB="${POSTGRES_DB:-gpt-load}"
REDIS_HOST="${REDIS_HOST:-redis}"
REDIS_PORT="${REDIS_PORT:-6379}"
MAX_WAIT_TIME=300  # 最大等待时间（秒）
RETRY_INTERVAL=2   # 重试间隔（秒）

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
}

# 环境检测函数
detect_environment() {
    log_info "Detecting container environment..."

    # 检测可用的shell
    if command -v bash >/dev/null 2>&1; then
        SHELL_TYPE="bash"
    else
        SHELL_TYPE="sh"
    fi
    log_info "Shell type: $SHELL_TYPE"

    # 检测网络工具
    if command -v nc >/dev/null 2>&1; then
        NETWORK_TOOL="nc"
    elif command -v telnet >/dev/null 2>&1; then
        NETWORK_TOOL="telnet"
    else
        log_warn "No network connectivity tool found"
        NETWORK_TOOL="none"
    fi
    log_info "Network tool: $NETWORK_TOOL"

    # 检测PostgreSQL客户端
    if command -v psql >/dev/null 2>&1; then
        PSQL_AVAILABLE="true"
        log_info "PostgreSQL client available"
    else
        PSQL_AVAILABLE="false"
        log_info "PostgreSQL client not available"
    fi
}

# 网络连接检查函数
check_connection() {
    local host=$1
    local port=$2
    local service_name=$3

    case $NETWORK_TOOL in
        "nc")
            nc -z "$host" "$port" >/dev/null 2>&1
            ;;
        "telnet")
            timeout 5 telnet "$host" "$port" >/dev/null 2>&1
            ;;
        *)
            # 如果没有网络工具，尝试直接连接应用
            return 0
            ;;
    esac
}

# 等待服务就绪函数
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local waited=0

    log_info "Waiting for $service_name to be ready..."

    while [ $waited -lt $MAX_WAIT_TIME ]; do
        if check_connection "$host" "$port" "$service_name"; then
            log_info "$service_name is ready!"
            return 0
        fi

        log_info "$service_name not ready yet, waiting... (${waited}s/${MAX_WAIT_TIME}s)"
        sleep $RETRY_INTERVAL
        waited=$((waited + RETRY_INTERVAL))
    done

    log_error "$service_name failed to become ready within $MAX_WAIT_TIME seconds"
    return 1
}

# 数据库健康检查函数
check_database_health() {
    log_info "Performing database health check..."

    if [ "$PSQL_AVAILABLE" = "true" ]; then
        # 如果有psql，执行详细检查
        if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT 1;" >/dev/null 2>&1; then
            log_info "Database connection successful"
            return 0
        else
            log_error "Database connection failed"
            return 1
        fi
    else
        # 如果没有psql，只检查端口连通性
        if check_connection "$POSTGRES_HOST" "$POSTGRES_PORT" "PostgreSQL"; then
            log_info "Database port is accessible"
            return 0
        else
            log_error "Database port is not accessible"
            return 1
        fi
    fi
}

# 主函数
main() {
    log_info "Starting GPT-Load initialization process..."
    log_info "Version: 2.0.0"

    # 环境检测
    detect_environment

    # 等待PostgreSQL就绪
    if ! wait_for_service "$POSTGRES_HOST" "$POSTGRES_PORT" "PostgreSQL"; then
        log_error "PostgreSQL startup timeout"
        exit 1
    fi

    # 等待Redis就绪
    if ! wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis"; then
        log_error "Redis startup timeout"
        exit 1
    fi

    # 给PostgreSQL额外时间完成初始化脚本
    log_info "Waiting for PostgreSQL initialization to complete..."
    sleep 5

    # 数据库健康检查
    if ! check_database_health; then
        log_error "Database health check failed"
        exit 1
    fi

    log_info "All dependencies are ready and healthy"
    log_info "Starting GPT-Load application..."

    # 启动应用
    exec /app/gpt-load
}

# 错误处理
trap 'log_error "Script interrupted"; exit 1' INT TERM

# 执行主函数
main "$@"