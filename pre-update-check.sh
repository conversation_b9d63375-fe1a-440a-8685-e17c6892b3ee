#!/bin/sh
# GPT-Load 更新前检查脚本
# 用于在更新前验证系统状态并创建备份

set -e

# 配置
BACKUP_DIR="./backup/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="./data/update_history.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查 Docker Compose 是否可用
check_docker_compose() {
    log "检查 Docker Compose..."
    if docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    elif command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        log "错误: Docker Compose 未找到"
        exit 1
    fi
    log "使用命令: $DOCKER_COMPOSE_CMD"
}

# 检查服务状态
check_services() {
    log "检查服务状态..."
    
    # 检查容器是否运行
    if ! $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
        log "警告: 部分服务未运行"
        return 1
    fi
    
    # 检查健康状态
    if ! $DOCKER_COMPOSE_CMD ps | grep -q "healthy"; then
        log "警告: 部分服务不健康"
        return 1
    fi
    
    log "所有服务运行正常"
    return 0
}

# 创建数据备份
create_backup() {
    log "创建备份到: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据目录
    if [ -d "./data" ]; then
        log "备份数据目录..."
        cp -r ./data "$BACKUP_DIR/"
    fi
    
    # 备份配置文件
    log "备份配置文件..."
    cp .env "$BACKUP_DIR/" 2>/dev/null || true
    cp docker-compose.yml "$BACKUP_DIR/"
    cp init-app-db.sh "$BACKUP_DIR/" 2>/dev/null || true
    
    # 备份数据库（如果服务运行中）
    if check_services; then
        log "备份数据库..."
        $DOCKER_COMPOSE_CMD exec -T postgres pg_dump -U postgres gpt-load > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || log "数据库备份失败（可能服务未运行）"
    fi
    
    log "备份完成: $BACKUP_DIR"
}

# 检查磁盘空间
check_disk_space() {
    log "检查磁盘空间..."
    
    # 获取当前目录的可用空间（MB）
    AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
    AVAILABLE_MB=$((AVAILABLE_SPACE / 1024))
    
    if [ $AVAILABLE_MB -lt 1024 ]; then
        log "警告: 可用磁盘空间不足 1GB ($AVAILABLE_MB MB)"
        return 1
    fi
    
    log "磁盘空间充足: ${AVAILABLE_MB}MB 可用"
    return 0
}

# 检查数据库版本
check_database_version() {
    log "检查数据库版本..."
    
    if check_services; then
        DB_VERSION=$($DOCKER_COMPOSE_CMD exec -T postgres psql -U postgres -d gpt-load -t -c "SELECT version FROM db_version ORDER BY applied_at DESC LIMIT 1;" 2>/dev/null | tr -d ' \n' || echo "unknown")
        log "当前数据库版本: $DB_VERSION"
    else
        log "无法检查数据库版本（服务未运行）"
    fi
}

# 主函数
main() {
    log "=== GPT-Load 更新前检查开始 ==="
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 执行检查
    check_docker_compose
    
    if ! check_disk_space; then
        log "错误: 磁盘空间不足，请清理后重试"
        exit 1
    fi
    
    check_database_version
    
    # 创建备份
    create_backup
    
    # 最终状态检查
    if check_services; then
        log "✅ 系统状态良好，可以安全更新"
        log "备份位置: $BACKUP_DIR"
        log "如需回滚，请运行: ./restore-backup.sh $BACKUP_DIR"
    else
        log "⚠️  部分服务异常，建议修复后再更新"
        log "备份已创建: $BACKUP_DIR"
    fi
    
    log "=== 更新前检查完成 ==="
}

# 执行主函数
main "$@"
