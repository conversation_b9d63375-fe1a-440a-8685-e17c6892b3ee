# GPT-Load 安全更新指南

## 概述
本指南提供了安全更新 GPT-Load 系统的步骤，包括数据备份、更新验证和故障恢复。

## 更新前准备

### 1. 运行更新前检查
```bash
# 给脚本执行权限
chmod +x pre-update-check.sh

# 运行检查
./pre-update-check.sh
```

这个脚本会：
- ✅ 检查系统状态
- ✅ 创建完整备份
- ✅ 验证磁盘空间
- ✅ 记录当前版本

### 2. 备份位置
备份会自动保存到 `./backup/YYYYMMDD_HHMMSS/` 目录，包含：
- 数据目录 (`./data`)
- 配置文件 (`.env`, `docker-compose.yml`)
- 数据库导出 (`database_backup.sql`)

## 安全更新步骤

### 方法1: 镜像更新（推荐）
```bash
# 1. 运行更新前检查
./pre-update-check.sh

# 2. 拉取最新镜像
docker compose pull

# 3. 重启服务
docker compose down
docker compose up -d

# 4. 检查服务状态
docker compose ps
docker compose logs gpt-load
```

### 方法2: 配置更新
```bash
# 1. 运行更新前检查
./pre-update-check.sh

# 2. 修改配置文件
# 编辑 .env 或 docker-compose.yml

# 3. 重启服务
docker compose down
docker compose up -d
```

## 更新后验证

### 1. 检查服务状态
```bash
# 查看所有服务状态
docker compose ps

# 检查健康状态（应该显示 healthy）
docker compose ps | grep healthy

# 查看应用日志
docker compose logs gpt-load
```

### 2. 功能测试
```bash
# 健康检查
curl http://localhost:3001/health

# 如果配置了认证，测试API
curl -H "Authorization: Bearer YOUR_AUTH_KEY" http://localhost:3001/api/groups
```

### 3. 数据库验证
```bash
# 检查数据库版本
docker compose exec postgres psql -U postgres -d gpt-load -c "SELECT * FROM db_version ORDER BY applied_at DESC LIMIT 1;"

# 检查表结构
docker compose exec postgres psql -U postgres -d gpt-load -c "\dt"
```

## 故障恢复

### 如果更新失败
```bash
# 使用备份恢复脚本
chmod +x restore-backup.sh
./restore-backup.sh ./backup/YYYYMMDD_HHMMSS
```

### 手动恢复步骤
```bash
# 1. 停止服务
docker compose down

# 2. 恢复数据目录
rm -rf ./data
cp -r ./backup/YYYYMMDD_HHMMSS/data ./

# 3. 恢复配置文件
cp ./backup/YYYYMMDD_HHMMSS/.env ./
cp ./backup/YYYYMMDD_HHMMSS/docker-compose.yml ./

# 4. 重启服务
docker compose up -d
```

## 常见问题解决

### 1. 容器启动失败
```bash
# 查看详细错误日志
docker compose logs

# 检查端口占用
netstat -tlnp | grep :3001
netstat -tlnp | grep :5432

# 清理并重启
docker compose down
docker system prune -f
docker compose up -d
```

### 2. 数据库连接失败
```bash
# 检查PostgreSQL状态
docker compose exec postgres pg_isready -U postgres

# 重置数据库（谨慎使用）
docker compose down
rm -rf ./data/postgres
docker compose up -d
```

### 3. 权限问题
```bash
# 修复数据目录权限
sudo chown -R $(id -u):$(id -g) ./data

# 修复脚本权限
chmod +x *.sh
```

## 最佳实践

### 1. 定期备份
```bash
# 设置定时任务（可选）
# 编辑 crontab: crontab -e
# 添加: 0 2 * * * cd /path/to/gpt-load && ./pre-update-check.sh
```

### 2. 监控日志
```bash
# 实时查看日志
docker compose logs -f gpt-load

# 查看错误日志
docker compose logs gpt-load | grep ERROR
```

### 3. 版本管理
- 记录每次更新的版本和时间
- 保留最近3个备份
- 测试环境先验证更新

## 紧急联系

如果遇到无法解决的问题：
1. 保留错误日志：`docker compose logs > error.log`
2. 记录操作步骤
3. 使用最近的备份恢复服务
4. 查看项目文档或提交issue

---

**注意**: 生产环境更新前，建议在测试环境先验证所有步骤。
