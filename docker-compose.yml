services:
  gpt-load:
    image: ghcr.io/tbphp/gpt-load:latest
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    container_name: gpt-load
    ports:
      - "${PORT:-3001}:${PORT:-3001}"
    env_file:
      - .env
    restart: always
    volumes:
      - ./data:/app/data
      - ./init-app-db.sh:/app/init-app-db.sh:ro
    stop_grace_period: ${SERVER_GRACEFUL_SHUTDOWN_TIMEOUT:-10}s
    entrypoint: ["/app/init-app-db.sh"]
    healthcheck:
      test: wget -q --spider -T 10 -O /dev/null http://localhost:${PORT:-3001}/health
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      warp:
        condition: service_started

  # 如果需要安装 MySQL、PostgreSQL 或 Redis，请取消以下注释并配置相应的环境变量。
  # 并且要在上方的depends_on中取消注释相应的依赖服务。

  # mysql:
  #   image: mysql:8.2
  #   container_name: gpt-load-mysql
  #   restart: always
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     MYSQL_ROOT_PASSWORD: 123456
  #     MYSQL_DATABASE: gpt-load
  #   volumes:
  #     - ./data/mysql:/var/lib/mysql
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
  #     interval: 5s
  #     timeout: 5s
  #     retries: 10

  postgres:
    image: "postgres:16"
    container_name: gpt-load-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: gpt-load
    ports:
      - "5432:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d gpt-load"]
      interval: 5s
      timeout: 5s
      retries: 10

  redis:
    image: redis:latest
    container_name: gpt-load-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3

  warp:
    image: caomingjun/warp
    container_name: gpt-load-warp
    restart: always
    # add removed rule back (https://github.com/opencontainers/runc/pull/3468)
    device_cgroup_rules:
      - 'c 10:200 rwm'
    ports:
      - "1081:1080"  # 使用不同的宿主机端口避免冲突
    environment:
      - WARP_SLEEP=2
      # - WARP_LICENSE_KEY= # optional, uncomment and add your license key for WARP+
      # - WARP_ENABLE_NAT=1 # enable nat if needed
    cap_add:
      # Docker already have them, these are for podman users
      - MKNOD
      - AUDIT_WRITE
      # additional required cap for warp, both for podman and docker
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv4.conf.all.src_valid_mark=1
      # uncomment for nat if needed
      # - net.ipv4.ip_forward=1
      # - net.ipv6.conf.all.forwarding=1
      # - net.ipv6.conf.all.accept_ra=2
    volumes:
      - ./data/warp-internal:/var/lib/cloudflare-warp
