-- Post-initialization fixes for GPT-Load database
-- This script fixes field types and constraints to match application expectations
-- Version: 1.0.1
-- Date: 2025-08-06

-- 创建简单的版本管理表（如果不存在）
CREATE TABLE IF NOT EXISTS db_version (
    id SERIAL PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    applied_at TIMESTAMP DEFAULT NOW()
);

-- 记录当前版本
INSERT INTO db_version (version, description)
VALUES ('1.0.1', 'Field type fixes and constraint updates')
ON CONFLICT DO NOTHING;

-- Fix field types in groups table
DO $$
BEGIN
    -- Check if upstreams column is TEXT and convert to JSONB
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'groups' 
        AND column_name = 'upstreams' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE groups ALTER COLUMN upstreams DROP DEFAULT;
        ALTER TABLE groups ALTER COLUMN upstreams TYPE jsonb USING upstreams::jsonb;
        ALTER TABLE groups ALTER COLUMN upstreams SET DEFAULT '[]'::jsonb;
        RAISE NOTICE 'Fixed upstreams column type to JSONB';
    END IF;

    -- Check if param_overrides column is TEXT and convert to JSONB
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'groups' 
        AND column_name = 'param_overrides' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE groups ALTER COLUMN param_overrides DROP DEFAULT;
        ALTER TABLE groups ALTER COLUMN param_overrides TYPE jsonb USING param_overrides::jsonb;
        ALTER TABLE groups ALTER COLUMN param_overrides SET DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Fixed param_overrides column type to JSONB';
    END IF;

    -- Check if config column is TEXT and convert to JSONB
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'groups' 
        AND column_name = 'config' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE groups ALTER COLUMN config DROP DEFAULT;
        ALTER TABLE groups ALTER COLUMN config TYPE jsonb USING config::jsonb;
        ALTER TABLE groups ALTER COLUMN config SET DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Fixed config column type to JSONB';
    END IF;
END $$;

-- Fix constraint name in api_keys table
DO $$
BEGIN
    -- Check if the old constraint exists and rename it
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'api_keys' 
        AND constraint_name = 'api_keys_key_value_key'
    ) THEN
        ALTER TABLE api_keys RENAME CONSTRAINT api_keys_key_value_key TO uni_api_keys_key_value;
        RAISE NOTICE 'Renamed constraint api_keys_key_value_key to uni_api_keys_key_value';
    END IF;
END $$;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'groups' 
AND column_name IN ('upstreams', 'param_overrides', 'config')
ORDER BY column_name;

SELECT constraint_name 
FROM information_schema.table_constraints 
WHERE table_name = 'api_keys' 
AND constraint_type = 'UNIQUE';
