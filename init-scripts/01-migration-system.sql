-- 数据库迁移管理系统
-- 此脚本创建迁移管理基础设施，必须在所有其他迁移之前执行

-- 创建迁移管理表
CREATE TABLE IF NOT EXISTS database_migrations (
    id SERIAL PRIMARY KEY,
    version VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    checksum VARCHAR(64) NOT NULL,
    executed_at TIMESTAMP DEFAULT NOW(),
    execution_time_ms INTEGER,
    status VARCHAR(20) DEFAULT 'completed',
    rollback_sql TEXT,
    created_by VARCHAR(100) DEFAULT 'system'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_migrations_version ON database_migrations(version);
CREATE INDEX IF NOT EXISTS idx_migrations_executed_at ON database_migrations(executed_at);
CREATE INDEX IF NOT EXISTS idx_migrations_status ON database_migrations(status);

-- 创建数据库配置表（用于存储数据库相关配置）
CREATE TABLE IF NOT EXISTS database_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 插入初始配置
INSERT INTO database_config (config_key, config_value, description) VALUES
    ('db_version', '1.0.0', 'Current database schema version'),
    ('last_backup', '', 'Timestamp of last successful backup'),
    ('migration_lock', 'false', 'Migration process lock to prevent concurrent migrations'),
    ('auto_backup_enabled', 'true', 'Enable automatic backup before migrations'),
    ('max_rollback_days', '30', 'Maximum days to keep rollback information')
ON CONFLICT (config_key) DO NOTHING;

-- 创建迁移日志表
CREATE TABLE IF NOT EXISTS migration_logs (
    id SERIAL PRIMARY KEY,
    migration_version VARCHAR(50),
    log_level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_migration_logs_version ON migration_logs(migration_version);
CREATE INDEX IF NOT EXISTS idx_migration_logs_created_at ON migration_logs(created_at);

-- 创建备份记录表
CREATE TABLE IF NOT EXISTS database_backups (
    id SERIAL PRIMARY KEY,
    backup_name VARCHAR(200) NOT NULL,
    backup_path TEXT,
    backup_size BIGINT,
    backup_type VARCHAR(50) DEFAULT 'full',
    created_before_migration VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    restored_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'completed'
);

-- 创建函数：记录迁移日志
CREATE OR REPLACE FUNCTION log_migration(
    p_version VARCHAR(50),
    p_level VARCHAR(10),
    p_message TEXT,
    p_details JSONB DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    INSERT INTO migration_logs (migration_version, log_level, message, details)
    VALUES (p_version, p_level, p_message, p_details);
END;
$$ LANGUAGE plpgsql;

-- 创建函数：检查迁移锁
CREATE OR REPLACE FUNCTION check_migration_lock() RETURNS BOOLEAN AS $$
DECLARE
    lock_status TEXT;
BEGIN
    SELECT config_value INTO lock_status 
    FROM database_config 
    WHERE config_key = 'migration_lock';
    
    RETURN COALESCE(lock_status, 'false') = 'true';
END;
$$ LANGUAGE plpgsql;

-- 创建函数：设置迁移锁
CREATE OR REPLACE FUNCTION set_migration_lock(p_locked BOOLEAN) RETURNS VOID AS $$
BEGIN
    UPDATE database_config 
    SET config_value = CASE WHEN p_locked THEN 'true' ELSE 'false' END,
        updated_at = NOW()
    WHERE config_key = 'migration_lock';
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取数据库版本
CREATE OR REPLACE FUNCTION get_db_version() RETURNS VARCHAR(50) AS $$
DECLARE
    version VARCHAR(50);
BEGIN
    SELECT config_value INTO version 
    FROM database_config 
    WHERE config_key = 'db_version';
    
    RETURN COALESCE(version, '0.0.0');
END;
$$ LANGUAGE plpgsql;

-- 创建函数：更新数据库版本
CREATE OR REPLACE FUNCTION update_db_version(p_version VARCHAR(50)) RETURNS VOID AS $$
BEGIN
    UPDATE database_config 
    SET config_value = p_version,
        updated_at = NOW()
    WHERE config_key = 'db_version';
    
    PERFORM log_migration('system', 'INFO', 'Database version updated to ' || p_version);
END;
$$ LANGUAGE plpgsql;

-- 记录迁移系统初始化
SELECT log_migration('01-migration-system', 'INFO', 'Migration system initialized successfully');

COMMENT ON TABLE database_migrations IS 'Tracks all database migrations and their execution status';
COMMENT ON TABLE database_config IS 'Stores database configuration and metadata';
COMMENT ON TABLE migration_logs IS 'Logs all migration-related activities';
COMMENT ON TABLE database_backups IS 'Records database backup information';
