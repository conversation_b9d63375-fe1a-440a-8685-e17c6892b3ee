#!/bin/sh
# GPT-Load 备份恢复脚本
# 用于从备份恢复系统状态

set -e

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <备份目录>"
    echo "示例: $0 ./backup/20250806_150000"
    exit 1
fi

BACKUP_DIR="$1"
LOG_FILE="./data/restore_history.log"

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误: 备份目录不存在: $BACKUP_DIR"
    exit 1
fi

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查 Docker Compose 命令
check_docker_compose() {
    if docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    elif command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        log "错误: Docker Compose 未找到"
        exit 1
    fi
}

# 停止服务
stop_services() {
    log "停止所有服务..."
    $DOCKER_COMPOSE_CMD down || true
    sleep 5
}

# 恢复数据
restore_data() {
    log "恢复数据目录..."
    
    # 备份当前数据（以防万一）
    if [ -d "./data" ]; then
        CURRENT_BACKUP="./data.backup.$(date +%Y%m%d_%H%M%S)"
        log "备份当前数据到: $CURRENT_BACKUP"
        mv ./data "$CURRENT_BACKUP"
    fi
    
    # 恢复数据
    if [ -d "$BACKUP_DIR/data" ]; then
        cp -r "$BACKUP_DIR/data" ./
        log "数据目录恢复完成"
    else
        log "警告: 备份中未找到数据目录"
    fi
}

# 恢复配置文件
restore_config() {
    log "恢复配置文件..."
    
    # 恢复 .env 文件
    if [ -f "$BACKUP_DIR/.env" ]; then
        cp "$BACKUP_DIR/.env" ./
        log ".env 文件已恢复"
    fi
    
    # 恢复 docker-compose.yml
    if [ -f "$BACKUP_DIR/docker-compose.yml" ]; then
        cp "$BACKUP_DIR/docker-compose.yml" ./
        log "docker-compose.yml 文件已恢复"
    fi
    
    # 恢复初始化脚本
    if [ -f "$BACKUP_DIR/init-app-db.sh" ]; then
        cp "$BACKUP_DIR/init-app-db.sh" ./
        log "init-app-db.sh 文件已恢复"
    fi
}

# 启动服务
start_services() {
    log "启动服务..."
    $DOCKER_COMPOSE_CMD up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
        log "服务启动成功"
    else
        log "警告: 部分服务可能未正常启动"
    fi
}

# 恢复数据库（如果有备份）
restore_database() {
    if [ -f "$BACKUP_DIR/database_backup.sql" ]; then
        log "恢复数据库..."
        
        # 等待数据库就绪
        log "等待数据库就绪..."
        sleep 10
        
        # 恢复数据库
        if $DOCKER_COMPOSE_CMD exec -T postgres psql -U postgres -d gpt-load < "$BACKUP_DIR/database_backup.sql" >/dev/null 2>&1; then
            log "数据库恢复成功"
        else
            log "警告: 数据库恢复失败，请手动检查"
        fi
    else
        log "未找到数据库备份文件，跳过数据库恢复"
    fi
}

# 验证恢复结果
verify_restore() {
    log "验证恢复结果..."
    
    # 检查服务健康状态
    sleep 30  # 等待健康检查
    
    if $DOCKER_COMPOSE_CMD ps | grep -q "healthy"; then
        log "✅ 恢复成功，服务运行正常"
        
        # 测试应用响应
        if curl -s http://localhost:3001/health >/dev/null 2>&1; then
            log "✅ 应用响应正常"
        else
            log "⚠️  应用可能未完全就绪，请稍后检查"
        fi
    else
        log "⚠️  部分服务可能异常，请检查日志"
    fi
}

# 主函数
main() {
    log "=== GPT-Load 备份恢复开始 ==="
    log "备份目录: $BACKUP_DIR"
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 确认操作
    echo "警告: 此操作将覆盖当前数据和配置"
    echo "备份目录: $BACKUP_DIR"
    echo -n "确认继续? (y/N): "
    read -r confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log "操作已取消"
        exit 0
    fi
    
    # 执行恢复
    check_docker_compose
    stop_services
    restore_data
    restore_config
    start_services
    restore_database
    verify_restore
    
    log "=== 备份恢复完成 ==="
    log "如有问题，请检查服务日志: $DOCKER_COMPOSE_CMD logs"
}

# 执行主函数
main "$@"
