Tu<PERSON> Aug  5 11:09:56 AM CST 2025: 成功更新到 v1.0.19
[2025-08-06 23:18:20] === GPT-Load 更新前检查开始 ===
[2025-08-06 23:18:20] 检查 Docker Compose...
[2025-08-06 23:18:20] 使用命令: docker compose
[2025-08-06 23:18:20] 检查磁盘空间...
[2025-08-06 23:18:20] 磁盘空间充足: 268203MB 可用
[2025-08-06 23:18:20] 检查数据库版本...
[2025-08-06 23:18:20] 检查服务状态...
[2025-08-06 23:18:21] 所有服务运行正常
[2025-08-06 23:18:21] 当前数据库版本: 
[2025-08-06 23:18:21] 创建备份到: ./backup/20250806_231820
[2025-08-06 23:18:21] 备份数据目录...
[2025-08-06 23:18:23] 备份配置文件...
[2025-08-06 23:18:23] 检查服务状态...
[2025-08-06 23:18:23] 所有服务运行正常
[2025-08-06 23:18:23] 备份数据库...
[2025-08-06 23:18:24] 备份完成: ./backup/20250806_231820
[2025-08-06 23:18:24] 检查服务状态...
[2025-08-06 23:18:24] 所有服务运行正常
[2025-08-06 23:18:24] ✅ 系统状态良好，可以安全更新
[2025-08-06 23:18:24] 备份位置: ./backup/20250806_231820
[2025-08-06 23:18:24] 如需回滚，请运行: ./restore-backup.sh ./backup/20250806_231820
[2025-08-06 23:18:24] === 更新前检查完成 ===
